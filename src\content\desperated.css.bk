.emoji-picker {
  max-height: 400px !important;
  overflow-y: auto !important;
}

.emoji-picker__filter-container {
  padding: 12px !important;
  border-bottom: 1px solid #e5e7eb !important;
  background: #f9fafb !important;
}

.emoji-picker__filter {
  position: relative !important;
}

.filter-input {
  width: 100% !important;
  padding: 8px 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  outline: none !important;
}

.filter-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 1px #3b82f6 !important;
}

.emoji-picker__content {
  padding: 0 !important;
}

.emoji-picker__sections-nav {
  display: flex !important;
  padding: 8px !important;
  border-bottom: 1px solid #e5e7eb !important;
  background: #f9fafb !important;
}

.emoji-picker__section-btn {
  padding: 6px !important;
  margin-right: 4px !important;
  border: none !important;
  background: none !important;
  border-radius: 4px !important;
  cursor: pointer !important;
}

.emoji-picker__section-btn.active {
  background: #e5e7eb !important;
}

.emoji-picker__scrollable-content {
  max-height: 280px !important;
  overflow-y: auto !important;
}

.emoji-picker__section {
  padding: 12px !important;
}

.emoji-picker__section-title-container {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 8px !important;
}

.emoji-picker__section-title {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #374151 !important;
  margin: 0 !important;
}

.emoji-picker__section-emojis {
  display: grid !important;
  grid-template-columns: repeat(6, 1fr) !important;
  gap: 8px !important;
}

.emoji-picker__section-emojis img {
  width: 32px !important;
  height: 32px !important;
  cursor: pointer !important;
  border-radius: 4px !important;
  padding: 4px !important;
  transition: background-color 0.15s !important;
}

.emoji-picker__section-emojis img:hover {
  background-color: #f3f4f6 !important;
}

.emoji-picker__section-emojis img:focus {
  background-color: #e5e7eb !important;
  outline: none !important;
}
.emoji-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #0000004d;
  z-index: 999999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.emoji-picker-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px #0000001a, 0 10px 10px -5px #0000000a;
  max-width: 400px;
  max-height: 500px;
  width: 90vw;
  display: flex;
  flex-direction: column;
  position: relative;
}
.emoji-picker-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9fafb;
  border-radius: 12px 12px 0 0;
}
.emoji-picker-title {
  font-weight: 600;
  color: #111827;
  margin: 0;
  font-size: 16px;
}
.emoji-picker-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px 8px;
  border-radius: 4px;
}
.emoji-picker-close:hover {
  background: #e5e7eb;
  color: #374151;
}
.emoji-picker-controls {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}
.scale-control-label {
  font-size: 14px;
  color: #374151;
  white-space: nowrap;
}
.scale-control-input {
  flex: 1;
  margin: 0 8px;
}
.scale-control-value {
  font-size: 14px;
  color: #6b7280;
  min-width: 40px;
  text-align: right;
}
.emoji-grid {
  display: grid;
  gap: 8px;
  padding: 16px;
  max-height: 320px;
  overflow-y: auto;
}
.emoji-grid.cols-2 {
  grid-template-columns: repeat(2, 1fr);
}
.emoji-grid.cols-3 {
  grid-template-columns: repeat(3, 1fr);
}
.emoji-grid.cols-4 {
  grid-template-columns: repeat(4, 1fr);
}
.emoji-grid.cols-5 {
  grid-template-columns: repeat(5, 1fr);
}
.emoji-grid.cols-6 {
  grid-template-columns: repeat(6, 1fr);
}
.emoji-grid.cols-8 {
  grid-template-columns: repeat(8, 1fr);
}
.emoji-button {
  border: none;
  background: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}
.emoji-button:hover {
  background: #f3f4f6;
}
.emoji-button:active {
  background: #e5e7eb;
}
.emoji-button img {
  display: block;
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}
.emoji-button-name {
  font-size: 11px;
  color: #6b7280;
  text-align: center;
  line-height: 1.2;
}
.emoji-grid::-webkit-scrollbar {
  width: 6px;
}
.emoji-grid::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}
.emoji-grid::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}
.emoji-grid::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
@media (max-width: 480px) {
  .emoji-picker-container {
    width: 95vw;
    max-width: none;
    margin: 10px;
  }
  .emoji-grid.cols-6 {
    grid-template-columns: repeat(4, 1fr);
  }
  .emoji-grid.cols-8 {
    grid-template-columns: repeat(5, 1fr);
  }
}
