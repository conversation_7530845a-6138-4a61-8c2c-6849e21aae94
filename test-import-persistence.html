<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试表情导入和分组顺序持久化</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-data {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>表情导入和分组顺序持久化测试</h1>
    
    <div class="test-section">
        <h2>测试1: 表情导入持久化</h2>
        <p>测试批量导入表情后数据是否正确保存到Chrome storage</p>
        
        <h3>测试数据:</h3>
        <div class="test-data">[
  {
    "name": "测试表情1",
    "url": "https://example.com/emoji1.png",
    "groupId": "测试分组1"
  },
  {
    "name": "测试表情2", 
    "url": "https://example.com/emoji2.png",
    "groupId": "测试分组1"
  },
  {
    "name": "测试表情3",
    "url": "https://example.com/emoji3.png", 
    "groupId": "测试分组2"
  }
]</div>
        
        <button onclick="testEmojiImport()">测试表情导入</button>
        <button onclick="checkStorageAfterImport()">检查存储状态</button>
        <div id="import-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>测试2: 分组顺序修改持久化</h2>
        <p>测试拖拽修改分组顺序后数据是否正确保存</p>
        
        <button onclick="testGroupReorder()">模拟分组重排序</button>
        <button onclick="checkStorageAfterReorder()">检查存储状态</button>
        <div id="reorder-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>同步存储测试</h2>
        <p>测试Chrome同步存储是否正确更新</p>
        <button onclick="checkSyncStorage()">检查同步存储</button>
        <button onclick="testSyncBackup()">测试同步备份</button>
        <div id="sync-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>当前存储状态</h2>
        <button onclick="showCurrentStorage()">显示本地存储数据</button>
        <button onclick="showSyncStorage()">显示同步存储数据</button>
        <div id="storage-display" class="result" style="display: none;"></div>
    </div>

    <script>
        // 模拟Chrome storage API (用于测试)
        if (typeof chrome === 'undefined') {
            window.chrome = {
                storage: {
                    local: {
                        data: {},
                        get: function(keys, callback) {
                            const result = {};
                            if (typeof keys === 'string') {
                                result[keys] = this.data[keys];
                            } else if (Array.isArray(keys)) {
                                keys.forEach(key => {
                                    result[key] = this.data[key];
                                });
                            } else if (typeof keys === 'object') {
                                Object.keys(keys).forEach(key => {
                                    result[key] = this.data[key] || keys[key];
                                });
                            }
                            setTimeout(() => callback(result), 10);
                        },
                        set: function(data, callback) {
                            Object.assign(this.data, data);
                            console.log('Local storage saved:', data);
                            setTimeout(() => callback && callback(), 10);
                        }
                    },
                    sync: {
                        data: {},
                        get: function(keys, callback) {
                            const result = {};
                            if (typeof keys === 'string') {
                                result[keys] = this.data[keys];
                            } else if (Array.isArray(keys)) {
                                keys.forEach(key => {
                                    result[key] = this.data[key];
                                });
                            } else if (typeof keys === 'object') {
                                Object.keys(keys).forEach(key => {
                                    result[key] = this.data[key] || keys[key];
                                });
                            }
                            setTimeout(() => callback(result), 10);
                        },
                        set: function(data, callback) {
                            Object.assign(this.data, data);
                            console.log('Sync storage saved:', data);
                            setTimeout(() => callback && callback(), 10);
                        }
                    }
                }
            };
        }

        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.style.display = 'block';
        }

        async function testEmojiImport() {
            const testData = [
                {
                    name: "测试表情1",
                    url: "https://example.com/emoji1.png",
                    groupId: "测试分组1"
                },
                {
                    name: "测试表情2", 
                    url: "https://example.com/emoji2.png",
                    groupId: "测试分组1"
                },
                {
                    name: "测试表情3",
                    url: "https://example.com/emoji3.png", 
                    groupId: "测试分组2"
                }
            ];
            
            try {
                // 模拟导入过程
                showResult('import-result', '开始测试表情导入...', true);
                
                // 这里应该调用实际的导入逻辑
                // 由于我们在测试环境中，我们模拟这个过程
                setTimeout(() => {
                    showResult('import-result', '表情导入测试完成。请在扩展的options页面中实际测试导入功能。', true);
                }, 1000);
                
            } catch (error) {
                showResult('import-result', `导入测试失败: ${error.message}`, false);
            }
        }

        async function checkStorageAfterImport() {
            chrome.storage.local.get(['emojiGroups'], (result) => {
                const groups = result.emojiGroups || [];
                const message = `当前存储中有 ${groups.length} 个分组，总共 ${groups.reduce((sum, g) => sum + (g.emojis?.length || 0), 0)} 个表情`;
                showResult('import-result', message, true);
            });
        }

        async function testGroupReorder() {
            showResult('reorder-result', '开始测试分组重排序...', true);
            
            // 模拟重排序过程
            setTimeout(() => {
                showResult('reorder-result', '分组重排序测试完成。请在扩展的options页面中实际测试拖拽重排序功能。', true);
            }, 1000);
        }

        async function checkStorageAfterReorder() {
            chrome.storage.local.get(['emojiGroups'], (result) => {
                const groups = result.emojiGroups || [];
                const orderInfo = groups.map(g => `${g.name}(order: ${g.order})`).join(', ');
                showResult('reorder-result', `分组顺序: ${orderInfo}`, true);
            });
        }

        async function checkSyncStorage() {
            chrome.storage.sync.get(['emojiExtensionBackup'], (result) => {
                const backup = result.emojiExtensionBackup;
                if (backup) {
                    const message = `同步存储中有 ${backup.groups?.length || 0} 个分组，时间戳: ${new Date(backup.timestamp).toLocaleString()}`;
                    showResult('sync-result', message, true);
                } else {
                    showResult('sync-result', '同步存储中没有备份数据', false);
                }
            });
        }

        async function testSyncBackup() {
            // 模拟创建备份
            const testBackup = {
                groups: [
                    { id: 'test-group', name: '测试分组', icon: '🧪', order: 0, emojis: [] }
                ],
                settings: { imageScale: 100, defaultGroup: 'test-group', showSearchBar: true, gridColumns: 4 },
                favorites: [],
                timestamp: Date.now(),
                version: '2.0'
            };

            chrome.storage.sync.set({ emojiExtensionBackup: testBackup }, () => {
                showResult('sync-result', '测试备份已创建到同步存储', true);
            });
        }

        async function showCurrentStorage() {
            chrome.storage.local.get(null, (result) => {
                const display = document.getElementById('storage-display');
                display.innerHTML = `<h4>本地存储:</h4><pre>${JSON.stringify(result, null, 2)}</pre>`;
                display.className = 'result';
                display.style.display = 'block';
            });
        }

        async function showSyncStorage() {
            chrome.storage.sync.get(null, (result) => {
                const display = document.getElementById('storage-display');
                display.innerHTML = `<h4>同步存储:</h4><pre>${JSON.stringify(result, null, 2)}</pre>`;
                display.className = 'result';
                display.style.display = 'block';
            });
        }
    </script>
</body>
</html>
