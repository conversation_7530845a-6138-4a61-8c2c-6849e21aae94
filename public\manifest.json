{"manifest_version": 3, "name": "Emoji Extension", "version": "1.0.0", "description": "An emoji extension built with Vue 3 and Vite.", "permissions": ["storage", "activeTab", "scripting"], "action": {"default_popup": "popup.html", "default_icon": {"48": "img/48.png", "64": "img/64.png", "128": "img/128.png", "512": "img/512.png", "1000": "img/1000.png", "1200": "img/1200.jpg"}}, "options_page": "options.html", "background": {"service_worker": "js/background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["js/content.js"]}], "icons": {"48": "img/48.png", "64": "img/64.png", "128": "img/128.png", "512": "img/512.png", "1000": "img/1000.png", "1200": "img/1200.jpg"}, "web_accessible_resources": [{"resources": ["assets/*", "js/*.css"], "matches": ["<all_urls>"]}]}